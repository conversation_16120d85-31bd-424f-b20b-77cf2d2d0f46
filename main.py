from nltk.stem import WordNetLemmatizer
from nltk.corpus import stopwords
# uncomment these lines to download the NLTK packages
#nltk.download('punkt')
#nltk.download('wordnet')
#nltk.download('stopwords')
#nltk.download('omw-1.4')

import argparse
import gensim
import numpy as np
from os.path import exists
from typing import List, Dict
from collections import defaultdict
from sklearn.linear_model import LinearRegression
import matplotlib.pyplot as plt

from utils.data import load_data, get_word_tokenized_corpus, get_data_property, get_data_chunks, ABSTRACT, N_CITATION
from utils.embeddings import train_fasttext_embedding, pretrain_fasttext_embedding, get_chunk_embeddings, save_fasttext, load_fasttext
from utils.features import get_features
from utils.controls import get_controls

def setup_args() -> argparse.Namespace:
    """Gets the command-line arguments used. Feel free to add your own here.

    Returns:
        argparse.Namespace: argparse object
    """
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_name", type=str, default='fasttext_model/wiki-news-300d-1M.vec', help="File to load model from.")
    parser.add_argument("--train_model", action='store_true', help="Whether to train existing model on data.")

    parser.add_argument("--data_file", type=str, default='data/dblp-ref-0.json', help="File/directory to load data from.")
    parser.add_argument("--data_file_type", type=str, help="If a directory is provided as the datafile, provide type to read")
    parser.add_argument("--chunk_embs_file", type=str, default='data/chunk_embs.txt', help="File to save/load chunks from.")
    parser.add_argument("--proj_dir", type=str, default='./word_embedding_measures/', help="Directory storing all data/models.")

    parser.add_argument("--limit", type=int, default=30000, help="Number of documents to train on")
    # TODO: allow user to create chunks based on length (e.g. I want a chunk to be a sentence or I want a chunk to be 5 tokens)
    parser.add_argument("--T", type=int, default=20, help="Number of chunks to make")

    # CLIP embedding arguments for Chinese travelogue analysis
    parser.add_argument("--embedding_type", type=str, default='fasttext', choices=['fasttext', 'clip'],
                       help="Type of embedding to use: 'fasttext' (default) or 'clip'")
    parser.add_argument("--language", type=str, default='en', choices=['en', 'zh'],
                       help="Language for text processing: 'en' (English) or 'zh' (Chinese)")
    parser.add_argument("--clip_model", type=str, default='ViT-B/32',
                       help="CLIP model name (when using --embedding_type clip). Options: ViT-B/32, ViT-B/16, ViT-L/14")

    args = parser.parse_args()

    if args.data_file[-1] == '/':
        assert args.data_file_type is not None
    return args
    

def setup(args: argparse.Namespace) -> tuple:
    """Sets up the embedding model (FastText or CLIP) and loads data.

    Args:
        args(argparse.Namespace): argparse object

    Returns:
        tuple[Union[gensim.models.fasttext.FastText, CLIPEmbedding], List[Dict]]:
        returns embedding model and data
    """
    print('Loading data...')
    data = load_data(args)
    documents = get_data_property(data, ABSTRACT)

    if args.embedding_type == 'clip':
        # CLIP embedding setup for Chinese travelogues
        print(f'Setting up CLIP embedding model: {args.clip_model}')
        try:
            from utils.embeddings import CLIPEmbedding
            clip_model = CLIPEmbedding(args.clip_model)
            print('✅ CLIP model loaded successfully')
            return clip_model, data
        except ImportError:
            print("❌ sentence-transformers not installed. Install with: pip install sentence-transformers")
            raise
        except Exception as e:
            print(f"❌ Failed to load CLIP model: {e}")
            raise
    else:
        # Original FastText setup
        proj_dir = args.proj_dir
        stemmer = WordNetLemmatizer()
        en_stop = set(stopwords.words('english'))

        ft_model = None
        if exists(proj_dir + args.model_name):
            print('Retrieving FastText model...')
            ft_model = load_fasttext(proj_dir + args.model_name)

            if args.train_model:
                print('Training FastText model...')
                tokenized_data = get_word_tokenized_corpus(documents, stemmer, en_stop)
                ft_model = train_fasttext_embedding(ft_model, tokenized_data)
                save_fasttext(ft_model, proj_dir + args.model_name.replace('.bin', '-trained.bin'))
        else:
            # tokenize data and train fasttext model
            print('Training FastText model...')
            tokenized_data = get_word_tokenized_corpus(documents, stemmer, en_stop)
            ft_model = pretrain_fasttext_embedding(tokenized_data)
            save_fasttext(ft_model, proj_dir + args.model_name)

        return ft_model, data


def setup_chunk_embeddings(args: argparse.Namespace, model, documents: List[str]) -> np.ndarray:
    """Setup chunk embeddings for both FastText and CLIP models.

    Args:
        args (argparse.Namespace): argparse object
        model: embedding model (FastText or CLIPEmbedding)
        documents (List[str]): list of documents

    Returns:
        np.ndarray: returns numpy array of chunk embeddings
    """

    if exists(args.proj_dir + args.chunk_embs_file):
        print('Loading existing chunk embeddings...')
        with open(args.proj_dir + args.chunk_embs_file, 'r+') as f:
            shape = tuple(map(int, f.readline()[1:].split(',')))
            limit = min(shape[0], args.limit)
            chunk_embs = np.loadtxt(f, skiprows=0, delimiter=',', max_rows=limit * args.T).reshape(limit, shape[1], shape[2])
    else:
        # Generate new chunk embeddings
        print('Generating chunk embeddings...')

        if args.embedding_type == 'clip':
            # CLIP embedding workflow for Chinese travelogues
            from utils.embeddings import get_clip_chunk_embeddings
            from utils.data import get_chinese_chunks

            print(f'Using Chinese chunking with T={args.T} chunks per document')
            chunks = [get_chinese_chunks(document, target_chunks=args.T) for document in documents]

            print('Generating CLIP embeddings...')
            chunk_embs = []
            for i, chunk_list in enumerate(chunks):
                if i % 100 == 0:
                    print(f'Processing document {i+1}/{len(chunks)}...')
                embeddings = get_clip_chunk_embeddings(model, chunk_list)
                chunk_embs.append(embeddings)

            chunk_embs = np.array(chunk_embs)
            print(f'Generated CLIP embeddings shape: {chunk_embs.shape}')
        else:
            # Original FastText workflow
            chunks = [get_data_chunks(document, T=args.T) for document in documents]
            chunk_embs = np.array([get_chunk_embeddings(model, chunk) for chunk in chunks])

        # Save embeddings
        header = ','.join(map(str, chunk_embs.shape))
        np.savetxt(args.proj_dir + args.chunk_embs_file, chunk_embs.reshape(-1, chunk_embs.shape[-1]), header=header, delimiter=',')
        print(f'Saved chunk embeddings to {args.proj_dir + args.chunk_embs_file}')

    return chunk_embs

if __name__ == "__main__":
    args = setup_args()

    # Configure data loading based on embedding type and language
    if args.embedding_type == 'clip' and args.language == 'zh':
        # Chinese travelogue configuration
        print("🇨🇳 Chinese Travelogue Analysis Mode")
        print(f"   Embedding: {args.embedding_type} ({args.clip_model})")
        print(f"   Language: {args.language}")
        print(f"   Chunks per document: {args.T}")
        args.strict_loading_list = []  # No strict loading for travelogue data
    else:
        # Original academic paper configuration
        print("📄 Academic Paper Analysis Mode")
        strict_loading_list = [ABSTRACT, N_CITATION, 'year', 'venue']
        args.strict_loading_list = strict_loading_list

    if exists(args.proj_dir + args.chunk_embs_file):
        print('Found existing chunks, loading data...')
        data = load_data(args)
        chunk_embs = setup_chunk_embeddings(args, None, None)
    else:
        print('No existing chunks, calling setup...')
        model, data = setup(args)
        documents = get_data_property(data, ABSTRACT)
        chunk_embs = setup_chunk_embeddings(args, model, documents)

    print('Getting narrative features...')
    features = [get_features(chunk_emb) for chunk_emb in chunk_embs]
    features_dict = defaultdict(list)

    # Handle different analysis modes
    if args.embedding_type == 'clip' and args.language == 'zh':
        # Chinese travelogue analysis - focus on narrative features
        print("Analyzing Chinese travelogue narrative features...")
        citation_counts = None
        controls = None
    else:
        # Original academic paper analysis
        citation_counts = get_data_property(data, N_CITATION)
        controls = get_controls(data)

    # move features into a dict that stores lists for every feature
    for d in features:
        for k, v in d.items():
            features_dict[k].append(v)

    '''
    for key, value in features_dict.items():
        print(key)
        print(value)
        print('-'*100)
    '''

    # if there is a missing value at the end of distances (sometimes there are only T-1 chunks)
    for i, l in enumerate(features_dict['distances']):
        for j in range(args.T - 1 - len(l)):
            l.append(np.nan)
            
    avg_distances = np.nanmean(np.array(features_dict['distances']), axis=0, dtype='float32')
    # plots the speed over the chunks
    plt.plot(list(range(args.T-1)), avg_distances)
    plt.savefig('distances.png') 

    if args.embedding_type == 'clip' and args.language == 'zh':
        # Chinese travelogue analysis - output narrative features
        print('\n🎯 Chinese Travelogue Narrative Analysis Results:')
        print('=' * 60)

        # Output results for each travelogue
        travel_ids = get_data_property(data, 'travelId')
        for i, (travel_id, feature_dict) in enumerate(zip(travel_ids, features)):
            if i < 5:  # Show first 5 examples
                print(f"\nTravelogue ID: {travel_id}")
                for feature_name in ['speed', 'volume', 'circuitousness']:
                    if feature_name in feature_dict:
                        print(f"  {feature_name}: {feature_dict[feature_name]:.6f}")

        # Summary statistics
        print(f"\n📊 Summary Statistics ({len(features)} travelogues):")
        for feature_name in ['speed', 'volume', 'circuitousness']:
            if feature_name in features_dict:
                values = np.array(features_dict[feature_name])
                valid_values = values[~np.isnan(values) & (values > 0) & ~np.isinf(values)]
                if len(valid_values) > 0:
                    print(f"  {feature_name}:")
                    print(f"    Mean: {np.mean(valid_values):.6f}")
                    print(f"    Std:  {np.std(valid_values):.6f}")
                    print(f"    Min:  {np.min(valid_values):.6f}")
                    print(f"    Max:  {np.max(valid_values):.6f}")

        print(f"\n✅ Chinese travelogue analysis complete!")
        print(f"   Processed {len(data)} travelogues with {args.T} chunks each")
        print(f"   Using {args.clip_model} CLIP embeddings")

    else:
        # Original academic paper regression analysis
        print('Getting regression coefficients...')
        for key, value in features_dict.items():
            if key == 'distances':
                continue

            # remove rows with invalid values (NaN, zero, negative, and infinite values)
            value_array = np.array(value)

            # Find indices of invalid values: NaN, <= 0, or infinite
            invalid_mask = np.isnan(value_array) | (value_array <= 0) | np.isinf(value_array)
            invalid_vals = np.argwhere(invalid_mask)

            # Create a fresh copy of controls for each iteration to avoid modifying the original
            controls_filtered = np.delete(controls, invalid_vals, axis=0)
            non_nan_citations = np.log(1 + np.delete(np.array(citation_counts), invalid_vals))

            # Only take log of positive values (invalid values already removed)
            valid_values = np.delete(value_array, invalid_vals)
            non_nan_vals = np.log(valid_values).reshape(-1, 1)
            dependent_vars = np.concatenate((non_nan_vals, controls_filtered), axis=1)

            clf = LinearRegression()
            clf.fit(dependent_vars, non_nan_citations)
            print(f'{key} coeff {clf.coef_[0]} mean {np.mean(non_nan_vals)}')

