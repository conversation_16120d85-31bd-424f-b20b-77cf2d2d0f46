#!/usr/bin/env python3
"""
Chinese Travelogue Narrative Analysis with CLIP Embeddings
Using Hugging Face transformers for openai/clip-vit-base-patch32

This module provides a complete pipeline for analyzing Chinese travelogues
and extracting narrative features (speed, volume, circuitousness).
"""

import json
import re
import numpy as np
import argparse
from typing import List, Dict, Tuple
from collections import defaultdict

class ChineseTextProcessor:
    """Chinese text processing with sentence segmentation and chunking."""
    
    def __init__(self, target_chunks: int = 100):
        """Initialize Chinese text processor.
        
        Args:
            target_chunks (int): Exact number of chunks to generate per document
        """
        self.target_chunks = target_chunks
        # Chinese punctuation pattern for sentence segmentation
        self.sentence_pattern = re.compile(r'[。！？；.!?;]|\n\s*')
    
    def get_chinese_chunks(self, document: str) -> List[str]:
        """Chunk Chinese document into exactly target_chunks using sentence-based segmentation.
        
        Critical: This function MUST return exactly target_chunks for mathematical consistency.
        
        Args:
            document (str): Chinese text document
        
        Returns:
            List[str]: List of exactly target_chunks text chunks
        """
        # Split into sentences using Chinese punctuation
        sentences = self.sentence_pattern.split(document)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if len(sentences) < self.target_chunks:
            # For short documents: repeat sentences to avoid zero embeddings
            repeat_factor = self.target_chunks // len(sentences) + 1
            chunks = (sentences * repeat_factor)[:self.target_chunks]
        elif len(sentences) > self.target_chunks:
            # For long documents: merge sentences to exact count
            chunks = self._merge_sentences_to_exact_count(sentences, self.target_chunks)
        else:
            chunks = sentences
        
        # Critical assertion: must be exactly target_chunks
        assert len(chunks) == self.target_chunks, f"Expected {self.target_chunks} chunks, got {len(chunks)}"
        assert all(chunk.strip() for chunk in chunks), "No empty chunks allowed"
        
        return chunks
    
    def _merge_sentences_to_exact_count(self, sentences: List[str], target_count: int) -> List[str]:
        """Merge sentences intelligently to reach exactly target_count chunks.
        
        Args:
            sentences (List[str]): List of sentences (more than target_count)
            target_count (int): Exact number of chunks needed
        
        Returns:
            List[str]: List of exactly target_count merged chunks
        """
        if len(sentences) <= target_count:
            return sentences
        
        # Calculate how many sentences to merge per chunk
        total_sentences = len(sentences)
        sentences_per_chunk = total_sentences / target_count
        
        chunks = []
        current_pos = 0.0
        
        for i in range(target_count):
            start_idx = int(current_pos)
            current_pos += sentences_per_chunk
            end_idx = int(current_pos) if i < target_count - 1 else total_sentences
            
            # Merge sentences in this range
            chunk_sentences = sentences[start_idx:end_idx]
            merged_chunk = ''.join(chunk_sentences)
            chunks.append(merged_chunk)
        
        return chunks


class CLIPEmbedding:
    """CLIP embedding class using Hugging Face transformers."""
    
    def __init__(self, model_name: str = "openai/clip-vit-base-patch32"):
        """Initialize CLIP embedding model from Hugging Face.
        
        Args:
            model_name (str): Hugging Face model name (default: openai/clip-vit-base-patch32)
        """
        try:
            from transformers import CLIPProcessor, CLIPModel
            import torch
            
            self.device = "cuda" if torch.cuda.is_available() else "cpu"

            model_name = r"D:\Code\paper\word_embedding_measures\model\saved\openai-clip"

            self.model = CLIPModel.from_pretrained(model_name).to(self.device)
            self.processor = CLIPProcessor.from_pretrained(model_name)
            self.model_name = model_name
            
            print(f"Successfully loaded CLIP model: {model_name}")
            print(f"Device: {self.device}")
            
        except ImportError as e:
            raise ImportError(
                "Hugging Face transformers required for CLIP embeddings. "
                "Install with: pip install transformers torch"
            ) from e
        except Exception as e:
            raise RuntimeError(f"Failed to load CLIP model '{model_name}': {e}") from e
    
    def get_embeddings(self, chunks: List[str]) -> np.ndarray:
        """Get CLIP text embeddings for chunks with L2 normalization.
        
        Args:
            chunks (List[str]): List of text chunks
        
        Returns:
            np.ndarray: L2-normalized embeddings of shape [len(chunks), 512]
        """
        if not chunks:
            return np.array([])
        
        import torch
        
        # Process text chunks
        inputs = self.processor(text=chunks, return_tensors="pt", padding=True, truncation=True)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Get text embeddings
        with torch.no_grad():
            text_features = self.model.get_text_features(**inputs)
            
            # Apply L2 normalization (critical for CLIP embeddings)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            
            # Convert to numpy
            embeddings = text_features.cpu().numpy()
        
        # Verify dimensions (should be 512 for CLIP ViT-B/32)
        expected_dim = 512
        if embeddings.shape[1] != expected_dim:
            print(f"Warning: Expected {expected_dim}D embeddings, got {embeddings.shape[1]}D")
        
        return embeddings


class NarrativeFeatureExtractor:
    """Extract narrative features from embeddings."""
    
    @staticmethod
    def get_speed(chunk_embeddings: np.ndarray) -> Tuple[List[float], float]:
        """Calculate narrative speed (average distance between consecutive chunks).
        
        Args:
            chunk_embeddings (np.ndarray): Array of chunk embeddings
        
        Returns:
            Tuple[List[float], float]: (distances, average_speed)
        """
        distances = []
        for i in range(len(chunk_embeddings) - 1):
            dist = np.linalg.norm(chunk_embeddings[i] - chunk_embeddings[i + 1])
            distances.append(dist)
        
        speed = np.mean(distances) if distances else 0.0
        return distances, speed
    
    @staticmethod
    def get_volume(chunk_embeddings: np.ndarray, tolerance: float = 0.01) -> float:
        """Calculate narrative volume using covariance determinant (suitable for high-dimensional embeddings).

        For high-dimensional embeddings (like 512D CLIP), convex hull is not practical.
        Instead, we use the determinant of the covariance matrix as a measure of volume.

        Args:
            chunk_embeddings (np.ndarray): Array of chunk embeddings
            tolerance (float): Tolerance for numerical stability

        Returns:
            float: Volume measure based on covariance determinant
        """
        if len(chunk_embeddings) < 2:
            return 0.0

        try:
            # Calculate covariance matrix
            cov_matrix = np.cov(chunk_embeddings.T)

            # Get eigenvalues for numerical stability
            eigenvals = np.linalg.eigvals(cov_matrix)

            # Filter out very small eigenvalues (numerical noise)
            eigenvals = eigenvals[eigenvals > tolerance]

            if len(eigenvals) == 0:
                return 0.0

            # Volume is proportional to the product of eigenvalues
            # Take geometric mean to normalize for dimensionality
            log_volume = np.sum(np.log(eigenvals)) / len(eigenvals)
            volume = np.exp(log_volume)

            return float(volume)

        except Exception as e:
            # Fallback: use variance-based measure
            try:
                variances = np.var(chunk_embeddings, axis=0)
                valid_variances = variances[variances > tolerance]
                if len(valid_variances) == 0:
                    return 0.0
                return float(np.mean(valid_variances))
            except:
                return 0.0
    
    @staticmethod
    def get_circuitousness(chunk_embeddings: np.ndarray) -> float:
        """Calculate narrative circuitousness using TSP-based path efficiency.
        
        Args:
            chunk_embeddings (np.ndarray): Array of chunk embeddings
        
        Returns:
            float: Circuitousness measure (ratio of actual path to direct path)
        """
        if len(chunk_embeddings) < 2:
            return 0.0
        
        # Calculate total path length (sum of consecutive distances)
        total_path_length = 0.0
        for i in range(len(chunk_embeddings) - 1):
            dist = np.linalg.norm(chunk_embeddings[i] - chunk_embeddings[i + 1])
            total_path_length += dist
        
        # Calculate direct path length (start to end)
        direct_path_length = np.linalg.norm(chunk_embeddings[0] - chunk_embeddings[-1])
        
        # Circuitousness is the ratio
        if direct_path_length > 0:
            return total_path_length / direct_path_length
        else:
            return 0.0
    
    def get_features(self, chunk_embeddings: np.ndarray) -> Dict[str, float]:
        """Extract all narrative features from chunk embeddings.
        
        Args:
            chunk_embeddings (np.ndarray): Array of chunk embeddings
        
        Returns:
            Dict[str, float]: Dictionary containing speed, volume, and circuitousness
        """
        distances, speed = self.get_speed(chunk_embeddings)
        volume = self.get_volume(chunk_embeddings)
        circuitousness = self.get_circuitousness(chunk_embeddings)
        
        return {
            'speed': speed,
            'volume': volume,
            'circuitousness': circuitousness,
            'distances': distances
        }


class ChineseTravelogueAnalyzer:
    """Main pipeline for Chinese travelogue narrative analysis."""

    def __init__(self, clip_model: str = "openai/clip-vit-base-patch32", target_chunks: int = 100):
        """Initialize the analyzer.

        Args:
            clip_model (str): Hugging Face CLIP model name
            target_chunks (int): Number of chunks per document
        """
        self.text_processor = ChineseTextProcessor(target_chunks)
        self.clip_model = CLIPEmbedding(clip_model)
        self.feature_extractor = NarrativeFeatureExtractor()
        self.target_chunks = target_chunks

    def load_travelogue_data(self, data_file: str) -> List[Dict]:
        """Load Chinese travelogue data from JSON file.

        Args:
            data_file (str): Path to JSON file containing travelogue data

        Returns:
            List[Dict]: List of travelogue dictionaries
        """
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            print(f"Loaded {len(data)} travelogues from {data_file}")

            # Validate data format
            if data and isinstance(data, list):
                first_item = data[0]
                if 'travelId' in first_item and 'source' in first_item:
                    print("✅ Data format validated: travelId + source fields found")
                else:
                    raise ValueError("Data must contain 'travelId' and 'source' fields")
            else:
                raise ValueError("Data must be a JSON array")

            return data

        except Exception as e:
            raise RuntimeError(f"Failed to load travelogue data: {e}")

    def analyze_single_travelogue(self, travelogue: Dict) -> Dict[str, float]:
        """Analyze a single travelogue and extract narrative features.

        Args:
            travelogue (Dict): Travelogue dictionary with 'travelId' and 'source'

        Returns:
            Dict[str, float]: Analysis result with travelId and narrative features
        """
        travel_id = travelogue.get('travelId', 'unknown')
        source_text = travelogue.get('source', '')

        if not source_text or not source_text.strip():
            print(f"Warning: Empty source text for travelogue {travel_id}")
            return {
                'travelId': str(travel_id),  # Ensure string type
                'speed': 0.0,
                'volume': 0.0,
                'circuitousness': 0.0
            }

        try:
            # Step 1: Chinese text chunking
            chunks = self.text_processor.get_chinese_chunks(source_text)

            # Step 2: CLIP embeddings
            embeddings = self.clip_model.get_embeddings(chunks)

            # Step 3: Feature extraction
            features = self.feature_extractor.get_features(embeddings)

            # Step 4: Format result with explicit type conversion for JSON serialization
            result = {
                'travelId': str(travel_id),
                'speed': float(features['speed']),  # Convert numpy float32 to Python float
                'volume': float(features['volume']),
                'circuitousness': float(features['circuitousness'])
            }

            return result

        except Exception as e:
            print(f"Error processing travelogue {travel_id}: {e}")
            # Return default values on error
            return {
                'travelId': str(travel_id),
                'speed': 0.0,
                'volume': 0.0,
                'circuitousness': 0.0
            }

    def analyze_batch(self, data: List[Dict], limit: int = None) -> List[Dict[str, float]]:
        """Analyze a batch of travelogues.

        Args:
            data (List[Dict]): List of travelogue dictionaries
            limit (int, optional): Maximum number of travelogues to process

        Returns:
            List[Dict[str, float]]: List of analysis results
        """
        if limit:
            data = data[:limit]

        results = []
        total = len(data)

        print(f"Analyzing {total} travelogues with {self.target_chunks} chunks each...")

        for i, travelogue in enumerate(data):
            if i % 10 == 0:
                print(f"Processing travelogue {i+1}/{total}...")

            try:
                result = self.analyze_single_travelogue(travelogue)
                results.append(result)
            except Exception as e:
                print(f"Error processing travelogue {i+1}: {e}")
                # Add default result to maintain order
                results.append({
                    'travelId': str(travelogue.get('travelId', f'error_{i}')),
                    'speed': 0.0,
                    'volume': 0.0,
                    'circuitousness': 0.0
                })

        return results

    def _ensure_json_serializable(self, results: List[Dict]) -> List[Dict]:
        """Ensure all values in results are JSON serializable.

        Args:
            results (List[Dict]): Analysis results

        Returns:
            List[Dict]: JSON-serializable results
        """
        serializable_results = []
        for result in results:
            serializable_result = {}
            for key, value in result.items():
                if isinstance(value, (np.floating, np.integer)):
                    serializable_result[key] = float(value)
                elif isinstance(value, np.ndarray):
                    serializable_result[key] = value.tolist()
                else:
                    serializable_result[key] = value
            serializable_results.append(serializable_result)
        return serializable_results

    def save_results(self, results: List[Dict], output_file: str):
        """Save analysis results to JSON file.

        Args:
            results (List[Dict]): Analysis results
            output_file (str): Output file path
        """
        # Ensure all values are JSON serializable
        serializable_results = self._ensure_json_serializable(results)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        print(f"Results saved to {output_file}")

    def print_summary_statistics(self, results: List[Dict]):
        """Print summary statistics of the analysis results.

        Args:
            results (List[Dict]): Analysis results
        """
        print("\n" + "="*60)
        print("📊 SUMMARY STATISTICS")
        print("="*60)

        # Extract valid values for each feature (convert to float to handle numpy types)
        speeds = [float(r['speed']) for r in results if float(r['speed']) > 0 and not np.isnan(float(r['speed']))]
        volumes = [float(r['volume']) for r in results if float(r['volume']) > 0 and not np.isnan(float(r['volume']))]
        circuitousness = [float(r['circuitousness']) for r in results if float(r['circuitousness']) > 0 and not np.isnan(float(r['circuitousness']))]

        print(f"Total travelogues processed: {len(results)}")
        print(f"Chunks per travelogue: {self.target_chunks}")
        print(f"CLIP model: {self.clip_model.model_name}")

        for feature_name, values in [('Speed', speeds), ('Volume', volumes), ('Circuitousness', circuitousness)]:
            if values:
                print(f"\n{feature_name}:")
                print(f"  Valid samples: {len(values)}")
                print(f"  Mean: {np.mean(values):.6f}")
                print(f"  Std:  {np.std(values):.6f}")
                print(f"  Min:  {np.min(values):.6f}")
                print(f"  Max:  {np.max(values):.6f}")
            else:
                print(f"\n{feature_name}: No valid values")


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Chinese Travelogue Narrative Analysis with CLIP")

    parser.add_argument("--data_file", type=str, required=True,
                       help="Path to Chinese travelogue JSON file")
    parser.add_argument("--output_file", type=str, default="travelogue_analysis_results.json",
                       help="Output file for analysis results")
    parser.add_argument("--clip_model", type=str, default="openai/clip-vit-base-patch32",
                       help="Hugging Face CLIP model name")
    parser.add_argument("--target_chunks", type=int, default=100,
                       help="Number of chunks per travelogue")
    parser.add_argument("--limit", type=int, default=None,
                       help="Maximum number of travelogues to process")

    args = parser.parse_args()

    print("🇨🇳 Chinese Travelogue Narrative Analysis")
    print("="*60)
    print(f"Data file: {args.data_file}")
    print(f"CLIP model: {args.clip_model}")
    print(f"Target chunks: {args.target_chunks}")
    print(f"Output file: {args.output_file}")
    if args.limit:
        print(f"Processing limit: {args.limit}")

    try:
        # Initialize analyzer
        analyzer = ChineseTravelogueAnalyzer(
            clip_model=args.clip_model,
            target_chunks=args.target_chunks
        )

        # Load data
        data = analyzer.load_travelogue_data(args.data_file)

        # Analyze travelogues
        results = analyzer.analyze_batch(data, limit=args.limit)

        # Save results
        analyzer.save_results(results, args.output_file)

        # Print summary
        analyzer.print_summary_statistics(results)

        print(f"\n✅ Analysis complete! Results saved to {args.output_file}")

    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
