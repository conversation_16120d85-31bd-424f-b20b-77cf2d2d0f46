# Chinese Travelogue Narrative Analysis - Quick Implementation Guide

based on word_embedding_measures and fit the chinese travelogue data. 

not reference to the original paper.

## 🎯 Goal
Extract three narrative features (speed, volume, circuitousness) from Chinese travelogues using pre-trained CLIP embeddings.

## 📋 Key Requirements
- **Input**: JSON with `travelId` and `source` fields
- **Output**: Three narrative features per travelogue
- **Chunks**: Exactly T=100 chunks per document (critical!)
- **Model**: Pre-trained CLIP (no training needed)

## 🔍 Critical Q&A

### Q: Do all travelogues need same chunk count?
**A**: YES! Exactly T=100 chunks required for mathematical consistency.

### Q: What if document has only 5 sentences?
**A**: Use content repetition, NOT zero padding:
```python
# Repeat sentences to reach exactly 100 chunks
repeat_factor = 100 // len(sentences) + 1
chunks = (sentences * repeat_factor)[:100]
```

### Q: Is training needed?
**A**: NO! Use pre-trained CLIP directly.

## 🏗️ Implementation Tasks

### 1. Data Loader (1 day)
```python
def get_data_property(data: List[Dict], prop: str = ABSTRACT) -> list:
    if prop == ABSTRACT:
        return [d.get('abstract') or d.get('source') for d in data]
    return [d.get(prop) for d in data]
```

### 2. Chinese Chunking (2 days)
```python
def get_chinese_chunks(document: str, target_chunks: int = 100) -> list:
    # Split by Chinese punctuation
    primary_pattern = re.compile(r'[。！？；.!?;]|\n\s*')
    sentences = primary_pattern.split(document)
    sentences = [s.strip() for s in sentences if s.strip()]
    
    if len(sentences) < target_chunks:
        # Repeat sentences to avoid zero embeddings
        repeat_factor = target_chunks // len(sentences) + 1
        chunks = (sentences * repeat_factor)[:target_chunks]
    elif len(sentences) > target_chunks:
        # Merge sentences to exact count
        chunks = merge_sentences_to_exact_count(sentences, target_chunks)
    else:
        chunks = sentences
    
    assert len(chunks) == target_chunks
    return chunks
```

### 3. CLIP Integration (2 days)
```python
from sentence_transformers import SentenceTransformer
import numpy as np

class CLIPEmbedding:
    def __init__(self, model_name: str = "openai/clip-vit-base-patch32"):
        self.model = SentenceTransformer(model_name)
    
    def get_embeddings(self, chunks: list) -> list:
        embeddings = self.model.encode(chunks)
        # Apply L2 normalization (important!)
        embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
        return embeddings
```

### 4. Pipeline Integration (1 day)
```bash
# Command to run
python main.py --embedding_type clip --language zh --data_file data.json --T 100
```

## 🚀 Expected Output
```python
{
    "travelId": "7687900",
    "speed": 0.234,           # Average distance between consecutive chunks
    "volume": 1.456,          # Semantic space occupied by narrative
    "circuitousness": 0.123   # How much narrative "wanders"
}
```

## ⚠️ Critical Points

### Short Document Handling
- **Solution**: Content repetition maintains semantic meaning

### CLIP Model Options
- **Primary**: `openai/clip-vit-base-patch32`
- **Alternative**: `sentence-transformers/clip-ViT-B-32`
- **Dimensions**: 512 (vs FastText's 300)
- **Normalization**: L2 required

### Data Format
```json
[
    {
        "travelId": "7687900",
        "source": "草原观赏季，专程从北京赶到..."
    }
]
```

## 🎯 Success Criteria
- ✅ Exactly T=100 chunks for all documents
- ✅ No zero embeddings in final matrix
- ✅ Consistent dimensions: [num_docs, 100, 512]
- ✅ Three meaningful features per travelogue

## 📝 Implementation Timeline
- **Week 1**: Data loader + Chinese chunking
- **Week 2**: CLIP integration + testing
- **Total**: ~6 days for complete implementation

---
*For detailed technical information, see readme_0809.md*