# Chinese Travelogue Narrative Analysis

This directory contains the implementation for analyzing Chinese travelogues using CLIP embeddings and extracting narrative features.

代码存在问题：
1. 使用clip得到的特征是经过L2标准化的，不利于计算volume！
2. 需要设置一个窗口，用于避免较短的句子特征带来的噪声，以及较长文本的信息缺失

需要深入理解论文后，再复现算法！！！


## Quick Start


```bash
cd model
python chinese_travelogue_analysis.py --data_file ../saved/data/data.json --output_file result.json --target_chunks 100 --limit 1000
```

- `--data_file` (required): Path to input JSON file
- `--output_file`: Output file name (default: `travelogue_analysis_results.json`)
- `--clip_model`: Hugging Face model name (default: `openai/clip-vit-base-patch32`)
- `--target_chunks`: Chunks per document (default: 100)
- `--limit`: Maximum documents to process (optional)

if you want to modify the clip model, you can change the `model_name` parameter in class CLIPEmbedding.

## Implementation Details

### Classes

1. **ChineseTextProcessor**
   - Handles Chinese sentence segmentation
   - Generates exactly T=100 chunks per document
   - Uses pattern: `[。！？；.!?;]|\n\s*`

2. **CLIPEmbedding**
   - Uses Hugging Face `openai/clip-vit-base-patch32`
   - Generates 512-dimensional embeddings
   - Applies L2 normalization automatically

3. **NarrativeFeatureExtractor**
   - Extracts speed, volume, and circuitousness
   - Works with any embedding dimension
   - Handles edge cases gracefully

4. **ChineseTravelogueAnalyzer**
   - Main pipeline orchestrator
   - Batch processing support
   - Summary statistics generation

### Input Format

JSON file with Chinese travelogue data:

```json
[
  {
    "travelId": "7687900",
    "source": "草原观赏季，专程从北京赶到内蒙古..."
  }
]
```

### Output Format

JSON file with analysis results:

```json
[
  {
    "travelId": "7687900",
    "speed": 1.408708,
    "volume": 0.945340,
    "circuitousness": 0.043203
  }
]
```
