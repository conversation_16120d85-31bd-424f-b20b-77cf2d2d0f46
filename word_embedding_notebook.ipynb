{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9dd25932", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from utils.data import load_data, get_word_tokenized_corpus, get_data_property, get_data_chunks\n", "from utils.embeddings import train_fasttext_embedding, get_chunk_embeddings, save_fasttext, load_fasttext\n", "from utils.features import get_features\n", "from main import setup_chunk_embeddings, setup"]}, {"cell_type": "code", "execution_count": 2, "id": "c2ede92d", "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload"]}, {"cell_type": "code", "execution_count": 3, "id": "2e4b703e", "metadata": {}, "outputs": [], "source": ["import nltk\n", "from nltk.stem import WordNetLemmatizer\n", "from nltk.corpus import stopwords"]}, {"cell_type": "code", "execution_count": 4, "id": "755ba897", "metadata": {}, "outputs": [], "source": ["import glob\n", "import numpy as np\n", "import pandas as pd\n", "from os.path import exists\n", "from sklearn.linear_model import Lasso"]}, {"cell_type": "code", "execution_count": 5, "id": "5f63be76", "metadata": {}, "outputs": [], "source": ["# SET THIS FLAG IF IMPORTING EMBS FROM MATLAB\n", "from_MATLAB = True\n", "# SET THIS FLAG FOR THE MODE (train, load, or test)\n", "mode = 'load'\n", "\n", "if mode == 'train':\n", "    # Train Model\n", "    class Args:\n", "    #     model_name = 'fasttext_model/ft_model.model'\n", "        model_name = 'fasttext_model/cc.en.300.bin'\n", "        data_file = 'data/dblp-ref-0.json'\n", "        chunk_embs_file = 'none'\n", "        proj_dir = './saved/'\n", "        limit = 30000\n", "        T = 20\n", "        train_model = True\n", "\n", "    args=Args()\n", "\n", "    ft_model, abstracts, citation_counts = setup(args)\n", "    chunk_embs = setup_chunk_embeddings(args, ft_model, abstracts)\n", "elif mode == 'load':\n", "    # Load Chunks\n", "    class Args:\n", "        model_name = 'fasttext_model/cc.en.300.bin'\n", "        data_file = 'data/dblp-ref-0.json'\n", "        chunk_embs_file = 'data/chunk_embs.txt'\n", "        proj_dir = './saved/'\n", "        limit = 30000\n", "        T = 20\n", "        train_model = False\n", "\n", "    args=Args()\n", "elif mode == 'test':\n", "    # Test sentences\n", "    # Load Chunks\n", "    class Args:\n", "        model_name = 'fasttext_model/wiki-news-300d-1M.vec'\n", "        proj_dir = './saved/'\n", "        T = 20\n", "        train_model = False\n", "\n", "    args=Args()\n", "    ft_model = load_fasttext(args.proj_dir + args.model_name)\n", "\n", "if from_MATLAB and mode != 'test':\n", "    df = pd.read_table('./saved/data/toubia_embs.txt', dtype=float, header=None, sep=',').fillna(0).values\n", "    chunk_embs = [np.trim_zeros(df[i], 'b').reshape(300,-1).transpose() for i in range(len(df))]\n", "elif mode != 'test':\n", "    chunk_embs = setup_chunk_embeddings(args, None, None)"]}, {"cell_type": "code", "execution_count": 6, "id": "a10d4b35", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "if mode == 'test':\n", "    sentences = ['The food here is ok but not worth the price.', \\\n", "                 'The food is mediocre and not worth the ridiculous price.', \\\n", "                 'The food is good but not worth the horrible customer service.', \\\n", "                 'The pizza and burgers are good but not worth the wait times.']\n", "\n", "    chunks = [get_data_chunks(sent, T=args.T) for sent in sentences]\n", "    chunk_embs = np.array([get_chunk_embeddings(ft_model, chunk) for chunk in chunks])\n", "    features = [get_features(chunk_emb) for chunk_emb in chunk_embs]\n", "    \n", "    for key in features[0].keys():\n", "        print('\\n' + key)\n", "        \n", "        if key == 'distances':\n", "            for i, feature in enumerate(features):\n", "                plt.plot(range(len(feature['distances'])), feature['distances'], label=f'Sentence{i}')\n", "            plt.legend()\n", "\n", "        else:\n", "            for feature in features:\n", "                print(feature[key])"]}, {"cell_type": "code", "execution_count": null, "id": "73d7ccdf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "639142b0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "67daeb0f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 33, "id": "66467edf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[-9.78689951e-03  3.30480348e-03 -1.81314411e-02 ...  1.36876420e-01\n", "   8.49039270e-03 -1.56296948e-02]\n", " [-1.29661071e-04 -3.06440671e-03 -1.05334746e-02 ...  1.30048305e-01\n", "   2.51627121e-02 -1.86733055e-02]\n", " [ 1.48067164e-02 -1.42041043e-02 -2.28097024e-03 ...  1.06595523e-01\n", "   5.02089567e-03 -6.82910485e-03]\n", " ...\n", " [ 1.50881856e-02  7.93417710e-03 -2.96333337e-02 ...  8.45185663e-02\n", "   1.61151902e-02 -1.60881855e-02]\n", " [-2.57022904e-03  1.13381681e-02 -1.46324432e-02 ...  8.37041993e-02\n", "   8.73816792e-03 -2.95305355e-03]\n", " [ 1.25537672e-02 -1.10582191e-02 -3.59301370e-02 ...  1.59184590e-01\n", "   2.17383562e-02 -2.25869866e-02]]\n", "[ 0  1  2 25 27 26 28 29 18 31 30 15 24 16 17 13 14 23  8 22  6  7  3  4\n", "  5 21 19 32  9 11 20 12 10]\n", "[[ 0.00944241  0.01239286 -0.00127589 ...  0.102075    0.01228438\n", "  -0.00957589]\n", " [ 0.0086363   0.0076163  -0.00077889 ...  0.13046593  0.02446111\n", "  -0.01562926]\n", " [ 0.00920984 -0.00067254  0.01364262 ...  0.1131623   0.01469754\n", "  -0.01412582]\n", " ...\n", " [ 0.03650433  0.00675591 -0.00093189 ...  0.11736732 -0.00091457\n", "  -0.00954724]\n", " [ 0.02865365 -0.00177774  0.00323394 ...  0.12384124  0.00299051\n", "  -0.00272409]\n", " [ 0.02665175  0.0013965  -0.01618755 ...  0.14120662 -0.00055253\n", "  -0.02071401]]\n", "[ 0 28  5 19 36 18 31 30 27 10  8 23 35 21 32 29 12  9 11  7  6 13 26 25\n", " 22 20 24 15 14 16 17 34 37  4  3  2 33  1]\n", "[[-2.01043173e-03 -2.14892086e-03 -1.95935253e-02 ...  1.06129857e-01\n", "   2.82086305e-03 -6.76942456e-03]\n", " [ 3.33446780e-03 -4.28510701e-04 -1.98863830e-02 ...  1.04085532e-01\n", "   1.46208512e-02 -3.42212792e-03]\n", " [ 2.75647046e-03 -1.10549015e-03 -1.38709806e-02 ...  1.40617255e-01\n", "   2.10670588e-02  6.17411727e-03]\n", " ...\n", " [-2.70199203e-03 -2.56848606e-02 -1.69342630e-02 ...  1.30804383e-01\n", "   2.15322707e-02 -1.03015937e-02]\n", " [-4.76244135e-03  1.40253518e-02 -1.80192489e-02 ...  5.59417849e-02\n", "   1.67934273e-02 -1.63239443e-03]\n", " [-1.11462777e-04  1.84592885e-02 -2.12616598e-02 ...  8.67169965e-02\n", "   1.00035574e-02 -3.49090904e-03]]\n", "[ 0 10 11  1 30  8  2 39 40  6 33 36 37 22 23 20 19 18 27 26 28 29  5 21\n", "  4 35 47 46 32 41 14 50 49 16 42 44 43 45 31 38  9 25 15 12 13 34 17 24\n", "  7 48  3]\n", "[[ 2.18740452e-03  1.22137326e-04 -2.00091602e-02 ...  1.53940077e-01\n", "   5.62251927e-03 -9.53664141e-03]\n", " [-4.11465212e-03  4.06300381e-03 -1.67963369e-02 ...  1.29580586e-01\n", "   7.25164877e-03 -1.96608060e-02]\n", " [ 3.79831916e-03 -2.07478951e-03 -1.78848736e-02 ...  1.40944539e-01\n", "   2.44773108e-02 -1.51264711e-02]\n", " ...\n", " [-1.12584261e-03  4.05580538e-03 -1.11325846e-02 ...  1.36877902e-01\n", "  -8.33707647e-04 -1.17202249e-02]\n", " [-3.90283443e-04  9.56639679e-03 -1.04834010e-02 ...  1.42226721e-01\n", "   7.79595157e-03 -9.81578964e-03]\n", " [-3.83449616e-03 -4.81666648e-03 -2.26108526e-02 ...  1.63082946e-01\n", "   1.79143411e-02 -9.26937992e-03]]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-33-f780ac21a70a>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mfeatures\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0mget_features\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mchunk_emb\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mfeature_list\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'circuitousness'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mchunk_emb\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mchunk_embs\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m<ipython-input-33-f780ac21a70a>\u001b[0m in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mfeatures\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0mget_features\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mchunk_emb\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mfeature_list\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'circuitousness'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mchunk_emb\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mchunk_embs\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m~/word_embedding_measures/utils/features.py\u001b[0m in \u001b[0;36mget_features\u001b[0;34m(chunk_emb, feature_list, volume_tolerance, circuitousness_tolerance)\u001b[0m\n\u001b[1;32m     19\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdistances\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     20\u001b[0m                 \u001b[0mdistances\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0m_\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mget_speed\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mchunk_emb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 21\u001b[0;31m             \u001b[0mfeature_val\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mget_circuitousness\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mchunk_emb\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtolerance\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcircuitousness_tolerance\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdistances\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdistances\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     22\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     23\u001b[0m         \u001b[0;32melif\u001b[0m \u001b[0mfeature\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlower\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;34m'distances'\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/word_embedding_measures/utils/features.py\u001b[0m in \u001b[0;36mget_circuitousness\u001b[0;34m(chunk_emb, tolerance, distances)\u001b[0m\n\u001b[1;32m    118\u001b[0m         \u001b[0;31m# do travelling salesman problem\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    119\u001b[0m         \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mchunk_emb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 120\u001b[0;31m         \u001b[0mroute\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtwo_opt\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mchunk_emb\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtolerance\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    121\u001b[0m         \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mroute\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    122\u001b[0m         \u001b[0mtsp\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msum\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlinalg\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnorm\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mchunk_emb\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mroute\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m-\u001b[0m \u001b[0mchunk_emb\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mroute\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mi\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mroute\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m-\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/word_embedding_measures/utils/algs.py\u001b[0m in \u001b[0;36mtwo_opt\u001b[0;34m(cities, improvement_threshold)\u001b[0m\n\u001b[1;32m     17\u001b[0m             \u001b[0;32mfor\u001b[0m \u001b[0mswap_last\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mswap_first\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mroute\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0;31m# to each of the cities following,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     18\u001b[0m                 \u001b[0mnew_route\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtwo_opt_swap\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mroute\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mswap_first\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mswap_last\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;31m# try reversing the order of these cities\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 19\u001b[0;31m                 \u001b[0mnew_distance\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpath_distance\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnew_route\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mcities\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;31m# and check the total distance with this modification.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     20\u001b[0m                 \u001b[0;32mif\u001b[0m \u001b[0mnew_distance\u001b[0m \u001b[0;34m<\u001b[0m \u001b[0mbest_distance\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0;31m# If the path distance is an improvement,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     21\u001b[0m                     \u001b[0mroute\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnew_route\u001b[0m \u001b[0;31m# make this the accepted best route\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/word_embedding_measures/utils/algs.py\u001b[0m in \u001b[0;36m<lambda>\u001b[0;34m(r, c)\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;31m## Code taken from https://stackoverflow.com/questions/25585401/\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;31m# Calculate the euclidian distance in n-space of the route r traversing cities c, ending at the path start.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 6\u001b[0;31m \u001b[0mpath_distance\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mlambda\u001b[0m \u001b[0mr\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mc\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msum\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlinalg\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnorm\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0mc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mp\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      7\u001b[0m \u001b[0;31m# Reverse the order of all elements from element i to element k in array r.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      8\u001b[0m \u001b[0mtwo_opt_swap\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mlambda\u001b[0m \u001b[0mr\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mconcatenate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/word_embedding_measures/utils/algs.py\u001b[0m in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;31m## Code taken from https://stackoverflow.com/questions/25585401/\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;31m# Calculate the euclidian distance in n-space of the route r traversing cities c, ending at the path start.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 6\u001b[0;31m \u001b[0mpath_distance\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mlambda\u001b[0m \u001b[0mr\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mc\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msum\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlinalg\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnorm\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0mc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mp\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      7\u001b[0m \u001b[0;31m# Reverse the order of all elements from element i to element k in array r.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      8\u001b[0m \u001b[0mtwo_opt_swap\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mlambda\u001b[0m \u001b[0mr\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mconcatenate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m<__array_function__ internals>\u001b[0m in \u001b[0;36mnorm\u001b[0;34m(*args, **kwargs)\u001b[0m\n", "\u001b[0;32m~/anaconda3/lib/python3.8/site-packages/numpy/linalg/linalg.py\u001b[0m in \u001b[0;36mnorm\u001b[0;34m(x, ord, axis, keepdims)\u001b[0m\n\u001b[1;32m   2528\u001b[0m             \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2529\u001b[0m                 \u001b[0msqnorm\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdot\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mx\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2530\u001b[0;31m             \u001b[0mret\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msqrt\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msqnorm\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2531\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mkeepdims\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2532\u001b[0m                 \u001b[0mret\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mret\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mreshape\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mndim\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["features = [get_features(chunk_emb, feature_list=['circuitousness']) for chunk_emb in chunk_embs]"]}, {"cell_type": "code", "execution_count": null, "id": "cdeb1eba", "metadata": {}, "outputs": [], "source": ["features"]}, {"cell_type": "code", "execution_count": null, "id": "0cd0648e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}