# Chinese Travelogue Narrative Analysis - Implementation Plan

based on word_embedding_measures and fit the chinese travelogue data. 

just have more infromation than readme_simple.md

not reference to the original paper.

## 🎯 Project Overview

This document outlines the implementation plan for extracting narrative features from Chinese travelogues using CLIP embeddings. The goal is to process Chinese travelogues and extract three narrative features (speed, volume, circuitousness) directly from text content without any training requirements.

## 📋 Requirements Summary

- **Dataset**: Chinese travelogues in JSON format with travelId and source text
- **Chunking**: Sentence-based segmentation targeting ~100 chunks per travelogue
- **Embeddings**: Pre-trained CLIP model (512-dimensional, no training needed)
- **Features**: Extract narrative analysis metrics (speed, volume, circuitousness)
- **Goal**: Direct feature extraction from text content only

## 🔍 Key Questions & Answers

### Q1: Do travelogues need the same number of chunks?
**Answer**: Yes, **exactly** the same number of chunks (T=100) for all travelogues.
- **Rationale**: Mathematical algorithms require consistent matrix dimensions [num_docs, T, embedding_dim]
- **Implementation**: Sentence-based chunking with merging/splitting to reach **exactly** T chunks
- **Requirement**: Must be exactly T=100, not flexible - this is critical for feature calculation


### Q2: Is model training required?
**Answer**: No training needed! CLIP is pre-trained and ready to use.
- **CLIP Advantage**: Pre-trained multilingual understanding including Chinese
- **Direct Processing**: Text → CLIP embeddings → narrative features
- **No Vocabulary Limits**: Handles any Chinese text without training

### Q3: Dataset Requirements
**Answer**: Only text content required in simple JSON format.

**Your Data Format** (Ready to use):
```json
[
    {
        "travelId": "7687900",
        "source": "草原观赏季，专程从北京赶到内蒙古乌兰布统大草原北部的汗苏鲁国际生态牧场..."
    }
]
```

**Important**: Travelogues with fewer than T sentences should be handled with content repetition to reach exactly T=100 chunks.

### Q4: Chinese Text Chunking Strategy

**Enhanced Punctuation Set**:
- **Regex**: `r'[。！？；.!?;]|\n\s*'` (primary) + `r'[：…]'` (secondary)

### Q5: CLIP Model Selection
**Primary Choice**: `openai/clip-vit-base-patch32`
**Alternative**: `sentence-transformers/clip-ViT-B-32`

**Rationale**:
- ✅ Pre-trained multilingual support
- ✅ Text-focused semantic understanding  
- ✅ 512-dimensional embeddings
- ✅ No training required

**Important**: Apply L2 normalization to CLIP embeddings


### Q6: What if a travelogue has only 5 sentences but needs T=100 chunks?

Solution: Content Repetition (Not Zero Padding)

```python
def handle_short_documents(sentences: list, target_chunks: int = 100) -> list:
    if len(sentences) < target_chunks:
        # Repeat sentences to reach target count
        repeat_factor = target_chunks // len(sentences) + 1
        chunks = (sentences * repeat_factor)[:target_chunks]
    return chunks
```

**Benefits**:
- ✅ Exactly T=100 chunks with meaningful content
- ✅ No zero embeddings
- ✅ Consistent matrix dimensions
- ✅ Preserves semantic information through repetition

## 🏗️ Implementation Tasks

😊Attention:You should implement the entire model under the model folder, rather than modifying the original code, and also add detailed comments to the code.

### Task 1: Data Loader Modification
**Priority**: High
**Estimated Time**: 1 day

**Objective**: Modify data loader to handle travelogue JSON format.

**Implementation**:
```python
# utils/data.py - Add travelogue support
def get_data_property(data: List[Dict], prop: str = ABSTRACT) -> list:
    if prop == ABSTRACT:
        # Support both 'abstract' and 'source' fields
        return [d.get('abstract') or d.get('source') for d in data]
    return [d.get(prop) for d in data]
```

**Verification Criteria**:
- [ ] Successfully loads travelogue JSON format
- [ ] Maps 'source' field to ABSTRACT for processing
- [ ] Handles missing fields gracefully

### Task 2: Chinese Text Chunking
**Priority**: High
**Estimated Time**: 2 days

**Objective**: Implement Chinese sentence-based chunking.

**Implementation**:
```python
# utils/data.py - Chinese chunking
import re

def get_chinese_chunks(document: str, target_chunks: int = 100) -> list:
    # Primary segmentation pattern
    primary_pattern = re.compile(r'[。！？；.!?;]|\n\s*')
    sentences = primary_pattern.split(document)
    sentences = [s.strip() for s in sentences if s.strip()]
    
    # Handle different cases to reach exactly target_chunks
    if len(sentences) < target_chunks:
        # For short documents: repeat sentences to avoid zero embeddings
        repeat_factor = target_chunks // len(sentences) + 1
        chunks = (sentences * repeat_factor)[:target_chunks]
    elif len(sentences) > target_chunks:
        # For long documents: merge sentences
        chunks = merge_sentences_to_exact_count(sentences, target_chunks)
    else:
        chunks = sentences
    
    assert len(chunks) == target_chunks  # Must be exactly target_chunks
    assert all(chunk.strip() for chunk in chunks)  # No empty chunks
    return chunks
```

**Verification Criteria**:
- [ ] Chinese sentences correctly segmented
- [ ] Chunk count exactly equals target (T=100) for all documents
- [ ] Reasonable length distribution across chunks### Task 3: CLIP Integration
**Priority**: Critical
**Estimated Time**: 2 days

**Objective**: Replace FastText with CLIP embeddings.

**Implementation**:
```python
# utils/embeddings.py - CLIP support
from sentence_transformers import SentenceTransformer
import numpy as np

class CLIPEmbedding:
    def __init__(self, model_name: str = "openai/clip-vit-base-patch32"):
        self.model = SentenceTransformer(model_name)
    
    def get_embeddings(self, chunks: list) -> list:
        # Get embeddings for text chunks
        embeddings = self.model.encode(chunks)
        # Apply L2 normalization
        embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
        return embeddings

def get_clip_chunk_embeddings(model: CLIPEmbedding, chunks: list) -> list:
    """Get CLIP embeddings for chunks (replaces FastText version)"""
    return model.get_embeddings(chunks)
```

**Verification Criteria**:
- [ ] CLIP model loads successfully
- [ ] Chinese text embeddings generated correctly (512-dim)
- [ ] L2 normalization applied
- [ ] Batch processing efficient

### Task 4: Pipeline Integration
**Priority**: Medium
**Estimated Time**: 1 day

**Objective**: Integrate all components into main pipeline.

**Command-Line Usage**:
```bash
# Process Chinese travelogues
python main.py --embedding_type clip --language zh --data_file data.json --T 100
```

**Verification Criteria**:
- [ ] End-to-end pipeline works
- [ ] Generates three features per travelogue
- [ ] Output format matches expectations

## 🚀 Expected Output

For each travelogue, you'll get:

saved/data/data.json:

```python
{
    "travelId": "7687900",
    "source": "草原观赏季，专程从北京赶到内蒙古乌兰布统大草原北部的汗苏鲁国际生态牧场...",
    "speed": 0.234,      # Average distance between consecutive chunks
    "volume": 1.456,     # Semantic space occupied by narrative
    "circuitousness": 0.123  # How much narrative "wanders"
}
```

## 📊 Usage Examples

### Basic Processing
```bash
python main.py --embedding_type clip --language zh --data_file data.json --T 100
```

### With Specific CLIP Model
```bash
python main.py --embedding_type clip --clip_model sentence-transformers/clip-ViT-B-32 --data_file data.json --T 100
```

## 🎯 Key Benefits

1. **No Training Required**: Use pre-trained CLIP directly
2. **Multilingual Support**: Handles Chinese text natively
3. **Consistent Analysis**: Same chunk count enables comparison
4. **Simple Input**: Only text content needed
5. **Rich Features**: Three narrative dimensions extracted automatically

## 📝 Next Steps

1. Implement data loader modifications
2. Add Chinese chunking logic with short document handling
3. Integrate CLIP embeddings
4. Test with sample travelogue data
5. Validate narrative features output


## 🔧 Technical Notes

### Chunking Strategy Details
- **Target**: Exactly 100 chunks per travelogue (fixed, not flexible)
- **Method**: Sentence-based with intelligent merging/splitting to reach exact count
- **Rationale**: Fixed chunk count required for matrix operations and feature comparison

### CLIP Model Details
- **Dimensions**: 512 
- **Normalization**: L2 normalization required
- **Language**: Native Chinese support
- **Performance**: No training overhead

### Feature Calculation
- **Speed**: Average Euclidean distance between consecutive chunk embeddings
- **Volume**: Minimum volume ellipsoid containing all chunk embeddings
- **Circuitousness**: Ratio of actual path to optimal (TSP) path through embedding space

All features are calculated using the same mathematical formulas as the original FastText implementation, ensuring consistency and comparability.