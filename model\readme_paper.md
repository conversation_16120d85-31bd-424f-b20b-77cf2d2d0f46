information generated by gpt5

https://chatgpt.com/share/689898c1-0a3c-8004-bc41-a3dae4d8b7f7

# 共同预处理步骤（来自主文 + 补充信息 SI）


* 分词，转为小写，保留句子边界，确保窗口不切割句子。
* 使用预训练的 **word2vec** 300维词向量（SI中也测试了GloVe；结果高度相关）。处理未登录词（OOV）：跳过或使用默认向量。
* 将文本划分为**非重叠窗口，约250词**（SI还测试了125和375——结果稳健）。如果一个句子会被切分，则完整包含该句子，因此窗口长度可能略超名义长度。将窗口编号为 $t=1\ldots T$。
* 窗口嵌入计算：

$$
x_t = \frac{1}{|c_t|}\sum_{w\in c_t} x_w\in\mathbb{R}^{300}
$$

其中 $c_t$ 表示第 $t$ 个窗口中的所有词。

* 使用**欧几里得距离**计算窗口向量之间的距离（作者解释使用欧氏距离的原因；余弦距离结果类似，但欧氏更契合几何度量）。
* 在建模时：对特征进行**对数变换**（如注明），并在回归前进行**标准化（z-score）**。

---

## 1) 语义速度（Speed）

**测量内容：** 相邻窗口之间的平均语义变化速率。

**公式**

* 步长距离：

$$
\text{distance}(t)=\|x_{t+1}-x_t\|_2
$$

* 平均速度：

$$
\text{speed}=\frac{1}{T-1}\sum_{t=1}^{T-1}\|x_{t+1}-x_t\|_2
$$

**实现说明**

* 计算 `dists = ||x[t+1]-x[t]||`，其中 $t=1$ 到 $T-1$，然后取均值：`speed = dists.mean()`。
* 作者在回归分析中对速度进行**对数变换**（然后再标准化）。
* 此外还计算了一些基于距离的辅助特征：
  - 距离的变异系数（对数变换后）
  - 距离趋势：距离序列与索引向量 `[1..T-1]` 的皮尔逊相关系数
  - 末段距离：`distance(T-1)`（对数变换后）

---

## 2) 语义体积（最小体积包围椭球，MVEE）

**测量内容：** 文档覆盖的语义“空间”大小——即能最小包围所有窗口点的椭球体积（经维度归一化）。

### 优化问题（SI 中的精确形式）

设点集为 $\{x_1,\dots,x_T\}$。SI 中将点以**最后一个点 $x_T$** 为中心进行平移（令 $y_t = x_t - x_T$，$t=1\dots T-1$），构造矩阵 $Y=[y_1\ ...\ y_{T-1}]$（300×(T−1)）。对 $Y$ 做奇异值分解（SVD），得到由这些点张成子空间的一组正交基 $S_1$。

在该子空间中求解 MVEE：寻找正定矩阵 $B$ 和中心 $d$（在子空间坐标下），最大化 $\det(B)$，满足以下约束：

$$
(S_1^\top y_t - d)^\top B (S_1^\top y_t - d)\le 1,\quad t=1,\dots,T-1
$$

$$
(-d)^\top B(-d)\le 1
$$

且 $B\succ 0$。（这是 SI 中的约束优化问题；他们使用 Moshtagh 的 MATLAB 代码求解。）

### 从解到数值体积

* 设 $B$ 在该子空间中的特征值为 $\lambda_1,\dots,\lambda_k$，对应椭球轴长为 $a_i = 1/\sqrt{\lambda_i}$。
* （原始）椭球体积正比于 $\prod_{i=1}^k a_i$。为了**跨维度归一化**，作者使用**几何平均数**（即归一化体积）：

$$
\text{volume}_{\text{norm}} = \Big(\prod_{i=1}^k a_i\Big)^{1/k}
$$

* 若 $Y$ 的秩 $k < T-1$（退化情况），SI 指出应使用 $S_1$ 的前 $k$ 列，并在 $k$ 维子空间中求解 MVEE（避免零体积异常）。

### 实际实现建议

* 对 $Y = [x_1-x_T,\dots,x_{T-1}-x_T]$ 进行 SVD，提取正交基 $S_1$。
* 将点投影到子空间：$z_t = S_1^\top y_t$。
* 在 $\{z_t\}$ 上求解 MVEE（包括原点约束）。
* 得到矩阵 $B$ 后，进行特征分解得到 $\lambda_i$ → 计算 $a_i$，再代入上述几何平均公式。
* 对接近零的特征值加入小的岭正则（如 `1e-8 * I`）以防止数值不稳定。

---

## 3) 语义迂回度（Circuitousness）

**测量内容：** 实际语义路径相对于访问相同窗口的最短路径有多“绕远”（起点和终点固定）。

**公式**

* 实际路径长度：

$$
L_{\text{actual}}=\sum_{t=1}^{T-1}\|x_{t+1}-x_t\|_2
$$

* 最短路径长度 $L_{\text{shortest}}$：从 $x_1$ 出发、终点为 $x_T$、访问所有中间点恰好一次的**最短哈密顿路径**（带固定端点的 TSP 变体）。
* 迂回度：

$$
\text{circuitousness}=\frac{L_{\text{actual}}}{L_{\text{shortest}}}
$$

（值为 1 表示实际路径即为最短路径；>1 表示更迂回。）

**实现说明**

* 这是一个类似 TSP 的优化问题（NP-hard）。SI 提到他们解决了该优化，但未指明具体求解器。实际使用建议如下：

  * 对于较小或中等规模的 $T$（几十个窗口）：使用精确求解器（如 Concorde、整数规划求解器），并加入起点和终点固定的约束。
  * 对于较大的 $T$：使用高质量启发式算法（如 LKH、OR-Tools 路由求解器、迭代改进法），同时强制固定起点和终点。
* 构造完整的成对距离矩阵 `D[i,j] = ||x_i - x_j||`。
* 求解 TSP，最小化路径上 D 的总和，要求起点为 1，终点为 T，得到 `L_shortest`。
* 迂回度为比值。作者在回归中对该值进行**对数变换**并标准化。

---

## 辅助度量（来自 SI）

* **基于距离的衍生指标**

  * 距离变化程度：距离的变异系数（对数变换后）
  * 距离趋势：`distance(t)` 与 `[1..T-1]` 的皮尔逊相关
  * 末段距离：`distance(T-1)`（对数变换后）

* **体积演化指标**

  * 定义 `volume_t`：点集 `{x_1..x_t}` 的（未归一化）椭球体积相对于单位球体积的比值。
  * 每步体积增长：`volume_increase(t) = volume_{t+1} / volume_t`。（SI 规定 `volume_increase(1) = volume_2`。）
  * 对 `volume_increase` 序列计算变异、趋势、末段值等辅助指标。

* SI 指出：由于最短路径的端点随时间变化，**逐时段的迂回度演化难以解释**。

---

## 稳健性与实践选择（SI 内容）

* 测试的窗口大小：125、250、375——主要结果稳健（见 SI 表 S3–S4）。
* 词向量：使用 word2vec；GloVe 和 doc2vec 也给出高度相关的度量结果（SI 表 S5）。
* 当数据点秩较低时，MVEE 在数据张成的子空间中计算（SI 提供了详细的 SVD 方法）。他们以**最后一个点 $x_T$** 为中心进行平移，并加入原点约束以提高数值稳定性。

---

## 数值与工程实现建议（实用）

* **预处理**：剔除过短文档（原文作者做了过滤），妥善处理 OOV 词；分窗时尊重句子边界。
* **距离计算**：使用 `numpy.linalg.norm`。若需模拟余弦相似性，可考虑对窗口向量做 L2 归一化；但原文使用原始欧氏距离。
* **MVEE 实现**：在投影子空间中实现 Khachiyan 算法（先 SVD 投影，再在 `z_t` 上求解 MVEE）。若矩阵数值不稳定，加入小的岭正则（如 `1e-8 * I`）。可使用 Moshtagh 的实现（SI 引用）或其 Python 移植版本。
* **TSP / 迂回度**：使用 OR-Tools 路由求解器并设置强制起点和终点，或使用 LKH/Concorde 获取更高精度解。当 $T \leq 100$ 时，启发式方法快速且准确；当 $T$ 达数百时，需权衡运行时间与近似精度。
* **特征变换**：对 **速度、体积、迂回度** 进行对数变换，然后 z-score 标准化，再用于建模。若目标是回归或类因果比较，应像原文一样加入控制变量（如文本长度、主题、固定效应）。

---

## 简化伪代码流程

1. 分词 → 划分为约 250 词的窗口（保留完整句子）。
2. 将词映射为 300-D word2vec 向量 → 计算每个窗口的 $x_t$。
3. 计算 `dists = ||x[t+1]-x[t]||`；`speed = mean(dists)`。同时计算距离相关的辅助指标。
4. 构造 $Y=[x_1-x_T,\dots,x_{T-1}-x_T]$。进行 SVD → 投影到子空间 $S_1$。在子空间中求解 MVEE → 得到特征值 → 轴长 $a_i=1/\sqrt{\lambda_i}$ → 计算归一化体积 `volume_norm = (∏ a_i)^{1/k}`。
5. 构造完整的成对距离矩阵，求解固定起点和终点的 TSP → 得到 `L_shortest`。计算 `L_actual`，并得到 `circuitousness = L_actual / L_shortest`。
6. 对所有特征进行对数变换和标准化，用于后续建模。

